version: '3.8'

services:
  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: homescreen_dev
      POSTGRES_USER: homescreen
      POSTGRES_PASSWORD: homescreen123
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data

  backend:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "8000:8000"
    environment:
      - DEBUG=True
      - DATABASE_URL=*********************************************/homescreen_dev
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=dev-secret-key
      - ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - db
      - redis
    command: python manage.py runserver 0.0.0.0:8000

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000/api
      - CHOKIDAR_USEPOLLING=true
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend

volumes:
  postgres_dev_data:
  redis_dev_data:
