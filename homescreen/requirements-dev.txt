# Development and code quality dependencies
-r requirements.txt

# Code formatting and linting
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
pylint>=3.0.0
mypy>=1.7.0

# Testing
pytest>=7.4.0
pytest-django>=4.7.0
pytest-cov>=4.1.0
pytest-mock>=3.12.0
factory-boy>=3.3.0
coverage>=7.3.0

# Security
bandit>=1.7.5
safety>=2.3.0

# Documentation
sphinx>=7.2.0
sphinx-rtd-theme>=1.3.0

# Development tools
pre-commit>=3.5.0
django-debug-toolbar>=4.2.0
django-extensions>=3.2.0

# Type stubs
django-stubs>=4.2.0
djangorestframework-stubs>=3.14.0
types-requests>=2.31.0
