Okay, creating a clear "purpose document" for your CLI agentic AI is a smart move. This will help ensure it understands the project's objectives and can assist you more effectively.

Based on our conversation, here's a draft document you can adapt for your AI. I've tried to make it concise and focused on the core purpose, especially for the MVP stage.

Project Definition: AI-Powered Home Screen Visualizer

1. Project Title:
Security Screen & Lifestyle Screen Visualizer (Internal Codename: [If you have one, insert here])

2. Overall Objective:
To develop a web application that provides users with a high-quality, realistic visual rendering of how selected security or lifestyle screens will appear on an image of their home. The primary goal is to enhance the sales process by allowing clients to accurately visualize the end result.

3. Primary User & Use Case (MVP Focus):

User: Sales representatives of screen installation companies (e.g., Boss Security Screens, Phifer, Twitchell product dealers).
Use Case: During in-home consultations, the sales rep will use the application to upload a photo of the client's home and instantly show them how different screen products/colors will look on their windows, doors, or patios.
4. Core Task of the Application:
To take a user-uploaded image of a home and specified screen preferences, and generate a modified version of that image which realistically depicts the chosen screens installed on the relevant areas of the home.

5. Key Features of the Visualizer (MVP Focus):

Image Upload: Allow users to upload an image of a home.
Area Selection:
Primary: Automated detection of windows, doors, and other screenable areas.
Fallback: Manual tool for users to select/draw the areas for screen application if automated detection is imperfect.
Screen Choice (Simplified for MVP): Allow selection of generic screen appearances (e.g., "dark security screen," "light solar screen") and representative colors (e.g., black, bronze, charcoal). The initial focus is on achieving a visually convincing general representation that has been validated as effective by stakeholders.
AI-Powered Rendering: Utilize an advanced AI model (e.g., multimodal LLM with image generation capabilities) to process the home image and apply the selected screen effect.
Output: Display a high-quality modified image showing the home with the rendered screens.
6. Desired Outcome/Success Metrics (MVP):

A functional prototype that sales representatives can successfully use in the field.
Generated images are considered "realistic" and "high-quality" by users and key stakeholders (e.g., achieved "10/10" rating from Boss Security Systems owner for general appearance).
Positive feedback from initial test users regarding ease of use and effectiveness as a sales aid.
7. Broader Vision (Post-MVP Context):
Future iterations will focus on:

Expanding the library to include highly accurate visual representations of specific branded screen products, textures, colors, and optical properties (e.g., opacity, reflectivity, weave patterns).
Potential for direct customer use on a website.
Further enhancements to realism and user customization options.
8. Technical Stack Context (for Agent Awareness):

Frontend: React
Backend: Django
Core Rendering Engine: AI-driven image generation/manipulation.
