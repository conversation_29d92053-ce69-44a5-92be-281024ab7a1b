from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from . import views

# Create a router and register our viewsets with it.
router = DefaultRouter()
router.register(r'screentypes', views.ScreenTypeViewSet, basename='screentype')
router.register(r'visualizations', views.VisualizationRequestViewSet, basename='visualizationrequest')
router.register(r'generated-images', views.GeneratedImageViewSet, basename='generatedimage')
router.register(r'profile', views.UserProfileViewSet, basename='userprofile')

# The API URLs are now determined automatically by the router.
urlpatterns = [
    path('', include(router.urls)),
    # Add URLs for auth (e.g., dj-rest-auth) later:
    # path('auth/', include('dj_rest_auth.urls')),
    # path('auth/registration/', include('dj_rest_auth.registration.urls')), # If using registration app
]
