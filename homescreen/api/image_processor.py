"""
Image processing service for generating homescreen visualizations.
"""

import os
import io
import time
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance
from django.core.files.base import ContentFile
from django.conf import settings
import logging

logger = logging.getLogger(__name__)


class HomescreenImageProcessor:
    """Service for processing images and generating homescreen visualizations."""

    def __init__(self):
        self.output_formats = ['JPEG', 'PNG']
        self.quality = 85
        self.max_output_size = (1920, 1080)

    def process_image(self, visualization_request):
        """
        Process an image and generate homescreen visualizations.

        Args:
            visualization_request: VisualizationRequest instance

        Returns:
            list: List of generated image file paths
        """
        try:
            # Mark request as processing
            visualization_request.mark_as_processing()

            # Load the original image
            visualization_request.update_progress(10, "Loading and validating image...")
            time.sleep(1)  # Simulate processing time
            original_image = Image.open(visualization_request.original_image.path)
            screen_type = visualization_request.screen_type

            # Generate different variations based on screen type
            visualization_request.update_progress(20, f"Preparing {screen_type.name} visualizations...")
            time.sleep(1)  # Simulate processing time
            generated_images = []

            if screen_type.name.lower() == 'security':
                generated_images.extend(self._generate_security_screens(original_image, visualization_request))
            elif screen_type.name.lower() == 'entertainment':
                generated_images.extend(self._generate_entertainment_screens(original_image, visualization_request))
            elif screen_type.name.lower() == 'smart home':
                generated_images.extend(self._generate_smart_home_screens(original_image, visualization_request))
            else:
                # Default processing
                generated_images.extend(self._generate_default_screens(original_image, visualization_request))

            # Mark request as complete
            visualization_request.mark_as_complete()

            logger.info(f"Successfully processed request {visualization_request.id}, generated {len(generated_images)} images")
            return generated_images

        except Exception as e:
            error_msg = f"Error processing image: {str(e)}"
            logger.error(error_msg)
            visualization_request.mark_as_failed(error_msg)
            return []

    def _generate_security_screens(self, original_image, request):
        """Generate security-themed homescreen visualizations."""
        generated_images = []

        # Security Camera Grid View
        request.update_progress(30, "Creating security camera grid layout...")
        time.sleep(2)  # Simulate processing time
        security_grid = self._create_security_camera_grid(original_image)
        generated_images.append(self._save_generated_image(security_grid, request, "security_grid"))

        # Security Dashboard View
        request.update_progress(60, "Building security dashboard interface...")
        time.sleep(2)  # Simulate processing time
        security_dashboard = self._create_security_dashboard(original_image)
        generated_images.append(self._save_generated_image(security_dashboard, request, "security_dashboard"))

        # Night Vision Style
        request.update_progress(90, "Applying night vision effects...")
        time.sleep(2)  # Simulate processing time
        night_vision = self._create_night_vision_style(original_image)
        generated_images.append(self._save_generated_image(night_vision, request, "night_vision"))

        return generated_images

    def _generate_entertainment_screens(self, original_image, request):
        """Generate entertainment-themed homescreen visualizations."""
        generated_images = []

        # TV/Media Center View
        tv_view = self._create_tv_interface(original_image)
        generated_images.append(self._save_generated_image(tv_view, request, "tv_interface"))

        # Gaming Dashboard
        gaming_view = self._create_gaming_interface(original_image)
        generated_images.append(self._save_generated_image(gaming_view, request, "gaming_interface"))

        # Cinema Mode
        cinema_view = self._create_cinema_mode(original_image)
        generated_images.append(self._save_generated_image(cinema_view, request, "cinema_mode"))

        return generated_images

    def _generate_smart_home_screens(self, original_image, request):
        """Generate smart home-themed homescreen visualizations."""
        generated_images = []

        # Home Control Dashboard
        control_dashboard = self._create_home_control_dashboard(original_image)
        generated_images.append(self._save_generated_image(control_dashboard, request, "control_dashboard"))

        # Environmental Monitoring
        env_monitor = self._create_environmental_monitor(original_image)
        generated_images.append(self._save_generated_image(env_monitor, request, "environmental_monitor"))

        # Device Status Grid
        device_grid = self._create_device_status_grid(original_image)
        generated_images.append(self._save_generated_image(device_grid, request, "device_status"))

        return generated_images

    def _generate_default_screens(self, original_image, request):
        """Generate default homescreen visualizations."""
        generated_images = []

        # Basic overlay
        basic_overlay = self._create_basic_overlay(original_image)
        generated_images.append(self._save_generated_image(basic_overlay, request, "basic_overlay"))

        # Enhanced version
        enhanced = self._create_enhanced_version(original_image)
        generated_images.append(self._save_generated_image(enhanced, request, "enhanced"))

        return generated_images

    def _create_security_camera_grid(self, original_image):
        """Create a security camera grid layout."""
        # Resize original to fit in grid
        grid_size = (1920, 1080)
        result = Image.new('RGB', grid_size, (20, 20, 20))

        # Create 2x2 grid with the original image in main position
        main_size = (960, 540)
        original_resized = original_image.copy()
        original_resized.thumbnail(main_size, Image.Resampling.LANCZOS)

        # Paste main image
        result.paste(original_resized, (0, 0))

        # Add security overlay elements
        draw = ImageDraw.Draw(result)

        # Add timestamp
        draw.rectangle([10, 10, 300, 50], fill=(0, 0, 0, 180))
        draw.text((20, 20), "CAM 01 - LIVE", fill=(0, 255, 0))
        draw.text((20, 35), "2025-05-25 23:45:12", fill=(255, 255, 255))

        # Add recording indicator
        draw.ellipse([main_size[0]-30, 10, main_size[0]-10, 30], fill=(255, 0, 0))

        # Add smaller camera feeds (simulated)
        small_feeds = [
            (960, 0, 1920, 270),    # Top right
            (960, 270, 1920, 540),  # Middle right
            (0, 540, 960, 810),     # Bottom left
            (960, 540, 1920, 810),  # Bottom right
        ]

        for i, (x1, y1, x2, y2) in enumerate(small_feeds):
            # Create a darkened version for other feeds
            feed_img = original_image.copy()
            feed_img.thumbnail((x2-x1, y2-y1), Image.Resampling.LANCZOS)
            enhancer = ImageEnhance.Brightness(feed_img)
            feed_img = enhancer.enhance(0.3)  # Darken
            result.paste(feed_img, (x1, y1))

            # Add camera label
            draw.rectangle([x1+5, y1+5, x1+100, y1+25], fill=(0, 0, 0, 180))
            draw.text((x1+10, y1+8), f"CAM {i+2:02d}", fill=(128, 128, 128))

        return result

    def _create_security_dashboard(self, original_image):
        """Create a security dashboard with status panels."""
        canvas_size = (1920, 1080)
        result = Image.new('RGB', canvas_size, (15, 15, 15))

        # Main image area (left side)
        main_area = (50, 50, 1200, 700)
        original_resized = original_image.copy()
        original_resized.thumbnail((main_area[2]-main_area[0], main_area[3]-main_area[1]), Image.Resampling.LANCZOS)
        result.paste(original_resized, main_area[:2])

        draw = ImageDraw.Draw(result)

        # Status panels (right side)
        panel_x = 1250
        panels = [
            ("SYSTEM STATUS", "ONLINE", (0, 255, 0)),
            ("MOTION DETECTED", "ZONE 1", (255, 165, 0)),
            ("RECORDING", "ACTIVE", (255, 0, 0)),
            ("ALERTS", "2 NEW", (255, 255, 0)),
        ]

        for i, (title, status, color) in enumerate(panels):
            y = 100 + i * 120
            # Panel background
            draw.rectangle([panel_x, y, panel_x+600, y+80], fill=(40, 40, 40), outline=(80, 80, 80))
            # Title
            draw.text((panel_x+20, y+10), title, fill=(200, 200, 200))
            # Status
            draw.text((panel_x+20, y+40), status, fill=color)

        return result

    def _create_night_vision_style(self, original_image):
        """Create a night vision style image."""
        # Convert to grayscale and apply green tint
        result = original_image.convert('L')
        result = result.convert('RGB')

        # Apply green tint for night vision effect
        pixels = result.load()
        for i in range(result.width):
            for j in range(result.height):
                gray = pixels[i, j][0]
                pixels[i, j] = (0, gray, 0)

        # Add noise for authentic night vision look
        enhancer = ImageEnhance.Contrast(result)
        result = enhancer.enhance(1.5)

        # Add crosshairs and HUD elements
        draw = ImageDraw.Draw(result)
        center_x, center_y = result.width // 2, result.height // 2

        # Crosshairs
        draw.line([center_x-20, center_y, center_x+20, center_y], fill=(0, 255, 0), width=2)
        draw.line([center_x, center_y-20, center_x, center_y+20], fill=(0, 255, 0), width=2)

        # HUD elements
        draw.text((20, 20), "NIGHT VISION MODE", fill=(0, 255, 0))
        draw.text((20, result.height-40), "INFRARED ACTIVE", fill=(0, 255, 0))

        return result

    def _create_tv_interface(self, original_image):
        """Create a TV/media center interface."""
        canvas_size = (1920, 1080)
        result = Image.new('RGB', canvas_size, (0, 0, 0))

        # Main content area
        content_area = (100, 100, 1500, 900)
        original_resized = original_image.copy()
        original_resized.thumbnail((content_area[2]-content_area[0], content_area[3]-content_area[1]), Image.Resampling.LANCZOS)
        result.paste(original_resized, content_area[:2])

        draw = ImageDraw.Draw(result)

        # TV interface elements
        # Top bar
        draw.rectangle([0, 0, 1920, 80], fill=(30, 30, 30))
        draw.text((50, 25), "📺 ENTERTAINMENT CENTER", fill=(255, 255, 255))
        draw.text((1600, 25), "🔊 VOLUME: 75%", fill=(255, 255, 255))

        # Bottom control bar
        draw.rectangle([0, 1000, 1920, 1080], fill=(20, 20, 20))
        controls = ["⏮️ PREV", "⏸️ PAUSE", "⏭️ NEXT", "🔄 REPEAT", "🔀 SHUFFLE"]
        for i, control in enumerate(controls):
            x = 200 + i * 200
            draw.text((x, 1020), control, fill=(255, 255, 255))

        # Side panel with content info
        draw.rectangle([1520, 100, 1820, 900], fill=(25, 25, 25), outline=(60, 60, 60))
        draw.text((1540, 120), "NOW PLAYING", fill=(200, 200, 200))
        draw.text((1540, 160), "Custom Content", fill=(255, 255, 255))
        draw.text((1540, 200), "Duration: --:--", fill=(150, 150, 150))

        return result

    def _create_gaming_interface(self, original_image):
        """Create a gaming interface overlay."""
        canvas_size = (1920, 1080)
        result = Image.new('RGB', canvas_size, (10, 10, 20))

        # Main game area
        game_area = (50, 50, 1400, 800)
        original_resized = original_image.copy()
        original_resized.thumbnail((game_area[2]-game_area[0], game_area[3]-game_area[1]), Image.Resampling.LANCZOS)
        result.paste(original_resized, game_area[:2])

        draw = ImageDraw.Draw(result)

        # Gaming HUD elements
        # Health bar
        draw.rectangle([50, 850, 350, 880], fill=(100, 0, 0))
        draw.rectangle([50, 850, 250, 880], fill=(0, 255, 0))
        draw.text((50, 890), "HEALTH: 75/100", fill=(255, 255, 255))

        # Score
        draw.text((1500, 50), "SCORE: 12,450", fill=(255, 255, 0))
        draw.text((1500, 80), "LEVEL: 3", fill=(255, 255, 255))

        # Mini-map area
        draw.rectangle([1450, 150, 1850, 550], fill=(0, 0, 50), outline=(100, 100, 255))
        draw.text((1460, 160), "MINI-MAP", fill=(150, 150, 255))

        # Chat/notifications area
        draw.rectangle([50, 950, 800, 1030], fill=(0, 0, 0, 180))
        draw.text((60, 960), "Player1: Nice shot!", fill=(0, 255, 0))
        draw.text((60, 980), "System: Achievement unlocked!", fill=(255, 255, 0))

        return result

    def _create_cinema_mode(self, original_image):
        """Create a cinema mode display."""
        canvas_size = (1920, 1080)
        result = Image.new('RGB', canvas_size, (0, 0, 0))

        # Cinematic aspect ratio (letterbox)
        cinema_height = 720
        y_offset = (1080 - cinema_height) // 2

        cinema_area = (0, y_offset, 1920, y_offset + cinema_height)
        original_resized = original_image.copy()
        original_resized.thumbnail((1920, cinema_height), Image.Resampling.LANCZOS)
        result.paste(original_resized, (0, y_offset))

        draw = ImageDraw.Draw(result)

        # Subtle cinema overlay
        draw.text((50, 50), "🎬 CINEMA MODE", fill=(255, 255, 255))
        draw.text((1600, 50), "4K HDR", fill=(255, 255, 255))

        # Progress bar at bottom
        progress_y = 1000
        draw.rectangle([100, progress_y, 1820, progress_y + 10], fill=(60, 60, 60))
        draw.rectangle([100, progress_y, 600, progress_y + 10], fill=(255, 0, 0))
        draw.text((100, progress_y + 20), "00:32:15 / 01:45:30", fill=(255, 255, 255))

        return result

    def _create_home_control_dashboard(self, original_image):
        """Create a smart home control dashboard."""
        canvas_size = (1920, 1080)
        result = Image.new('RGB', canvas_size, (25, 25, 35))

        # Main image as background (dimmed)
        bg_image = original_image.copy()
        bg_image.thumbnail(canvas_size, Image.Resampling.LANCZOS)
        enhancer = ImageEnhance.Brightness(bg_image)
        bg_image = enhancer.enhance(0.3)
        result.paste(bg_image, (0, 0))

        draw = ImageDraw.Draw(result)

        # Control panels
        panels = [
            (100, 100, "🌡️ TEMPERATURE", "72°F", (0, 150, 255)),
            (500, 100, "💡 LIGHTING", "8/12 ON", (255, 255, 0)),
            (900, 100, "🔒 SECURITY", "ARMED", (0, 255, 0)),
            (1300, 100, "🎵 AUDIO", "ZONE 2", (255, 100, 255)),
            (100, 400, "🌿 GARDEN", "WATERING", (0, 255, 100)),
            (500, 400, "⚡ ENERGY", "2.4 kW", (255, 150, 0)),
            (900, 400, "📹 CAMERAS", "4 ACTIVE", (255, 255, 255)),
            (1300, 400, "🚗 GARAGE", "CLOSED", (150, 150, 150)),
        ]

        for x, y, title, value, color in panels:
            # Panel background
            draw.rectangle([x, y, x+300, y+200], fill=(40, 40, 50, 200), outline=(80, 80, 100))
            # Title
            draw.text((x+20, y+20), title, fill=(200, 200, 200))
            # Value
            draw.text((x+20, y+60), value, fill=color)
            # Status indicator
            draw.ellipse([x+250, y+20, x+280, y+50], fill=color)

        # Header
        draw.rectangle([0, 0, 1920, 60], fill=(20, 20, 30))
        draw.text((50, 20), "🏠 SMART HOME CONTROL CENTER", fill=(255, 255, 255))
        draw.text((1600, 20), "ONLINE", fill=(0, 255, 0))

        return result

    def _create_environmental_monitor(self, original_image):
        """Create an environmental monitoring display."""
        canvas_size = (1920, 1080)
        result = Image.new('RGB', canvas_size, (15, 25, 15))

        # Background image (subtle)
        bg_image = original_image.copy()
        bg_image.thumbnail(canvas_size, Image.Resampling.LANCZOS)
        enhancer = ImageEnhance.Brightness(bg_image)
        bg_image = enhancer.enhance(0.2)
        result.paste(bg_image, (0, 0))

        draw = ImageDraw.Draw(result)

        # Environmental data panels
        draw.rectangle([50, 50, 1870, 150], fill=(20, 40, 20))
        draw.text((70, 70), "🌍 ENVIRONMENTAL MONITORING SYSTEM", fill=(0, 255, 100))
        draw.text((70, 100), "Last Updated: 2025-05-25 23:45:00", fill=(150, 255, 150))

        # Data grid
        metrics = [
            ("Temperature", "72.3°F", "Normal", (0, 255, 0)),
            ("Humidity", "45%", "Optimal", (0, 255, 0)),
            ("Air Quality", "Good", "AQI: 42", (0, 255, 0)),
            ("CO2 Level", "420 ppm", "Normal", (255, 255, 0)),
            ("Light Level", "850 lux", "Bright", (255, 255, 0)),
            ("Noise Level", "35 dB", "Quiet", (0, 255, 0)),
        ]

        for i, (metric, value, status, color) in enumerate(metrics):
            x = 100 + (i % 3) * 500
            y = 250 + (i // 3) * 200

            # Metric panel
            draw.rectangle([x, y, x+400, y+150], fill=(30, 50, 30), outline=(60, 100, 60))
            draw.text((x+20, y+20), metric, fill=(200, 255, 200))
            draw.text((x+20, y+60), value, fill=color)
            draw.text((x+20, y+100), status, fill=(150, 200, 150))

        return result

    def _create_device_status_grid(self, original_image):
        """Create a device status grid."""
        canvas_size = (1920, 1080)
        result = Image.new('RGB', canvas_size, (20, 20, 30))

        draw = ImageDraw.Draw(result)

        # Header
        draw.rectangle([0, 0, 1920, 80], fill=(30, 30, 50))
        draw.text((50, 25), "📱 DEVICE STATUS GRID", fill=(255, 255, 255))

        # Device grid
        devices = [
            ("Smart TV", "Living Room", "ON", (0, 255, 0)),
            ("Thermostat", "Main Floor", "AUTO", (0, 255, 255)),
            ("Door Lock", "Front Door", "LOCKED", (0, 255, 0)),
            ("Camera", "Driveway", "RECORDING", (255, 0, 0)),
            ("Lights", "Kitchen", "DIMMED", (255, 255, 0)),
            ("Speaker", "Bedroom", "OFF", (128, 128, 128)),
            ("Sensor", "Garage", "MOTION", (255, 165, 0)),
            ("Outlet", "Office", "ON", (0, 255, 0)),
        ]

        for i, (device, location, status, color) in enumerate(devices):
            x = 100 + (i % 4) * 400
            y = 150 + (i // 4) * 250

            # Device panel
            draw.rectangle([x, y, x+350, y+200], fill=(40, 40, 60), outline=(80, 80, 120))

            # Device icon area
            draw.rectangle([x+20, y+20, x+80, y+80], fill=(60, 60, 90))
            draw.text((x+35, y+35), "📱", fill=(255, 255, 255))

            # Device info
            draw.text((x+100, y+30), device, fill=(255, 255, 255))
            draw.text((x+100, y+60), location, fill=(200, 200, 200))
            draw.text((x+100, y+90), f"Status: {status}", fill=color)

            # Status indicator
            draw.ellipse([x+300, y+30, x+330, y+60], fill=color)

        return result

    def _create_basic_overlay(self, original_image):
        """Create a basic overlay."""
        result = original_image.copy()
        draw = ImageDraw.Draw(result)

        # Simple overlay elements
        draw.rectangle([20, 20, 300, 80], fill=(0, 0, 0, 180))
        draw.text((30, 30), "HOMESCREEN DISPLAY", fill=(255, 255, 255))
        draw.text((30, 50), "Generated Visualization", fill=(200, 200, 200))

        return result

    def _create_enhanced_version(self, original_image):
        """Create an enhanced version of the image."""
        result = original_image.copy()

        # Enhance contrast and brightness
        enhancer = ImageEnhance.Contrast(result)
        result = enhancer.enhance(1.2)

        enhancer = ImageEnhance.Brightness(result)
        result = enhancer.enhance(1.1)

        # Add subtle border
        draw = ImageDraw.Draw(result)
        width, height = result.size
        border_width = 5
        draw.rectangle([0, 0, width-1, height-1], outline=(100, 100, 100), width=border_width)

        return result

    def _save_generated_image(self, image, request, suffix):
        """Save a generated image and create a GeneratedImage record."""
        from .models import GeneratedImage

        # Resize if too large
        if image.size[0] > self.max_output_size[0] or image.size[1] > self.max_output_size[1]:
            image.thumbnail(self.max_output_size, Image.Resampling.LANCZOS)

        # Save to BytesIO
        output = io.BytesIO()
        image.save(output, format='JPEG', quality=self.quality)
        output.seek(0)

        # Create filename
        filename = f"generated_{request.id}_{suffix}.jpg"

        # Create GeneratedImage record
        generated_image = GeneratedImage(request=request)
        generated_image.generated_image.save(
            filename,
            ContentFile(output.getvalue()),
            save=True
        )

        return generated_image