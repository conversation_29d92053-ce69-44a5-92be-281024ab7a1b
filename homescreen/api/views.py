import logging
from django.db import transaction
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from django.core.cache import cache
from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.exceptions import ValidationError, PermissionDenied
from rest_framework.pagination import PageNumberPagination
from django_filters.rest_framework import DjangoFilterBackend
from .models import VisualizationRequest, ScreenType, GeneratedImage, UserProfile
from .serializers import (
    VisualizationRequestListSerializer,
    VisualizationRequestDetailSerializer,
    VisualizationRequestCreateSerializer,
    ScreenTypeSerializer,
    GeneratedImageSerializer,
    UserProfileSerializer
)
# from .tasks import process_image_request # Import later if using Celery

logger = logging.getLogger(__name__)


class StandardResultsSetPagination(PageNumberPagination):
    """Standard pagination class for API responses."""
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100


class IsOwnerOrReadOnly(permissions.BasePermission):
    """Custom permission to only allow owners of an object to edit it."""

    def has_object_permission(self, request, view, obj):
        # Read permissions are allowed for any request,
        # so we'll always allow GET, HEAD or OPTIONS requests.
        if request.method in permissions.SAFE_METHODS:
            return True

        # Write permissions are only allowed to the owner of the object.
        return obj.user == request.user


class ScreenTypeViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint for viewing screen types.
    Supports filtering, searching, and caching.
    """
    serializer_class = ScreenTypeSerializer
    permission_classes = [permissions.AllowAny]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_active']
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'sort_order', 'created_at']
    ordering = ['sort_order', 'name']

    def get_queryset(self):
        """Return active screen types by default."""
        queryset = ScreenType.objects.all()

        # Filter by active status if not explicitly requested
        if not self.request.query_params.get('is_active'):
            queryset = queryset.filter(is_active=True)

        return queryset

    @method_decorator(cache_page(60 * 15))  # Cache for 15 minutes
    def list(self, request, *args, **kwargs):
        """List screen types with caching."""
        return super().list(request, *args, **kwargs)

    @action(detail=False, methods=['get'])
    def active(self, request):
        """Get only active screen types."""
        queryset = self.get_queryset().filter(is_active=True)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)


class VisualizationRequestViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing visualization requests.
    Supports filtering, searching, pagination, and optimized queries.
    """
    permission_classes = [permissions.IsAuthenticated, IsOwnerOrReadOnly]
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'screen_type']
    search_fields = ['screen_type__name']
    ordering_fields = ['created_at', 'updated_at', 'status']
    ordering = ['-created_at']

    def get_queryset(self):
        """
        Return visualization requests for the authenticated user with optimized queries.
        """
        user = self.request.user
        queryset = VisualizationRequest.objects.filter(user=user)

        # Optimize queries based on action
        if self.action == 'list':
            queryset = queryset.select_related('screen_type', 'user')
        elif self.action in ['retrieve', 'update', 'partial_update']:
            queryset = queryset.select_related('screen_type', 'user').prefetch_related('results')

        return queryset

    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'list':
            return VisualizationRequestListSerializer
        elif self.action == 'create':
            return VisualizationRequestCreateSerializer
        return VisualizationRequestDetailSerializer

    def get_object(self):
        """Get object with permission check."""
        obj = super().get_object()

        # Ensure user can only access their own requests
        if obj.user != self.request.user:
            raise PermissionDenied("You can only access your own requests.")

        return obj

    @transaction.atomic
    def perform_create(self, serializer):
        """
        Create a new visualization request with proper error handling.
        """
        try:
            # Save the instance with the current user
            instance = serializer.save(user=self.request.user, status='pending')

            logger.info(f"VisualizationRequest created: ID={instance.id}, User={self.request.user.username}")

            # Trigger AI processing
            self._trigger_ai_processing(instance)

        except Exception as e:
            logger.error(f"Error creating visualization request: {str(e)}")
            raise ValidationError("Failed to create visualization request. Please try again.")

    def perform_update(self, serializer):
        """Update request with validation."""
        instance = serializer.instance

        # Only allow updates to pending requests
        if instance.status not in ['pending', 'failed']:
            raise ValidationError("Cannot update requests that are processing or completed.")

        serializer.save()
        logger.info(f"VisualizationRequest updated: ID={instance.id}")

    def perform_destroy(self, instance):
        """Delete request with proper cleanup."""
        # Only allow deletion of pending or failed requests
        if instance.status in ['processing']:
            raise ValidationError("Cannot delete requests that are currently processing.")

        logger.info(f"VisualizationRequest deleted: ID={instance.id}")
        super().perform_destroy(instance)

    @action(detail=True, methods=['post'])
    def retry(self, request, pk=None):
        """Retry a failed visualization request."""
        instance = self.get_object()

        if instance.status != 'failed':
            return Response(
                {'error': 'Only failed requests can be retried.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Reset status and clear error message
        instance.status = 'pending'
        instance.error_message = ''
        instance.save()

        # TODO: Trigger AI processing
        # self._trigger_ai_processing(instance)

        logger.info(f"VisualizationRequest retry: ID={instance.id}")

        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get user's request statistics."""
        user = request.user
        queryset = self.get_queryset()

        stats = {
            'total': queryset.count(),
            'pending': queryset.filter(status='pending').count(),
            'processing': queryset.filter(status='processing').count(),
            'completed': queryset.filter(status='complete').count(),
            'failed': queryset.filter(status='failed').count(),
        }

        return Response(stats)

    def _trigger_ai_processing(self, instance):
        """
        Trigger AI processing for the visualization request.
        """
        from .image_processor import HomescreenImageProcessor
        import threading

        def process_in_background():
            """Process the image in a background thread."""
            try:
                processor = HomescreenImageProcessor()
                generated_images = processor.process_image(instance)
                logger.info(f"Successfully processed request {instance.id}, generated {len(generated_images)} images")
            except Exception as e:
                logger.error(f"Error processing request {instance.id}: {str(e)}")
                instance.mark_as_failed(str(e))

        # Start processing in background thread
        thread = threading.Thread(target=process_in_background)
        thread.daemon = True
        thread.start()

        logger.info(f"AI processing started for request {instance.id}")


class GeneratedImageViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint for viewing generated images.
    Users can only view images from their own requests.
    """
    serializer_class = GeneratedImageSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['request']
    ordering_fields = ['generated_at']
    ordering = ['-generated_at']

    def get_queryset(self):
        """Return generated images for the authenticated user's requests."""
        user = self.request.user
        return GeneratedImage.objects.filter(
            request__user=user
        ).select_related('request', 'request__user', 'request__screen_type')

    def get_object(self):
        """Get object with permission check."""
        obj = super().get_object()

        # Ensure user can only access images from their own requests
        if obj.request.user != self.request.user:
            raise PermissionDenied("You can only access images from your own requests.")

        return obj


class UserProfileViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing user profiles.
    Users can only view and edit their own profile.
    """
    serializer_class = UserProfileSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """Return only the current user's profile."""
        return UserProfile.objects.filter(user=self.request.user)

    def get_object(self):
        """Get or create the user's profile."""
        profile, created = UserProfile.objects.get_or_create(user=self.request.user)
        return profile

    def list(self, request, *args, **kwargs):
        """Return the user's profile as a single object."""
        profile = self.get_object()
        serializer = self.get_serializer(profile)
        return Response(serializer.data)

    def create(self, request, *args, **kwargs):
        """Profiles are created automatically, so redirect to update."""
        return self.update(request, *args, **kwargs)

    def perform_update(self, serializer):
        """Update the user's profile."""
        serializer.save(user=self.request.user)
        logger.info(f"UserProfile updated: User={self.request.user.username}")


# TODO: Add authentication views using dj-rest-auth or custom implementation