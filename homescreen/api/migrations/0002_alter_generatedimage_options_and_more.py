# Generated by Django 5.2 on 2025-05-25 17:25

import api.models
import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='generatedimage',
            options={'ordering': ['-generated_at'], 'verbose_name': 'Generated Image', 'verbose_name_plural': 'Generated Images'},
        ),
        migrations.AlterModelOptions(
            name='screentype',
            options={'ordering': ['sort_order', 'name'], 'verbose_name': 'Screen Type', 'verbose_name_plural': 'Screen Types'},
        ),
        migrations.AlterModelOptions(
            name='userprofile',
            options={'ordering': ['-created_at'], 'verbose_name': 'User Profile', 'verbose_name_plural': 'User Profiles'},
        ),
        migrations.AlterModelOptions(
            name='visualizationrequest',
            options={'ordering': ['-created_at'], 'verbose_name': 'Visualization Request', 'verbose_name_plural': 'Visualization Requests'},
        ),
        migrations.AddField(
            model_name='generatedimage',
            name='file_size',
            field=models.PositiveIntegerField(blank=True, help_text='File size in bytes', null=True),
        ),
        migrations.AddField(
            model_name='generatedimage',
            name='image_height',
            field=models.PositiveIntegerField(blank=True, help_text='Image height in pixels', null=True),
        ),
        migrations.AddField(
            model_name='generatedimage',
            name='image_width',
            field=models.PositiveIntegerField(blank=True, help_text='Image width in pixels', null=True),
        ),
        migrations.AddField(
            model_name='screentype',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='screentype',
            name='is_active',
            field=models.BooleanField(default=True, help_text='Whether this screen type is available for selection'),
        ),
        migrations.AddField(
            model_name='screentype',
            name='sort_order',
            field=models.PositiveIntegerField(default=0, help_text='Order for displaying screen types'),
        ),
        migrations.AddField(
            model_name='screentype',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='company_name',
            field=models.CharField(blank=True, help_text='Company or organization name', max_length=100),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='userprofile',
            name='phone_number',
            field=models.CharField(blank=True, help_text='Contact phone number', max_length=20),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='visualizationrequest',
            name='error_message',
            field=models.TextField(blank=True, help_text='Error message if processing failed'),
        ),
        migrations.AddField(
            model_name='visualizationrequest',
            name='processing_completed_at',
            field=models.DateTimeField(blank=True, help_text='When processing completed', null=True),
        ),
        migrations.AddField(
            model_name='visualizationrequest',
            name='processing_started_at',
            field=models.DateTimeField(blank=True, help_text='When processing started', null=True),
        ),
        migrations.AddField(
            model_name='visualizationrequest',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name='generatedimage',
            name='generated_image',
            field=models.ImageField(help_text='Generated image with screen overlay', upload_to=api.models.upload_to_generated),
        ),
        migrations.AlterField(
            model_name='generatedimage',
            name='request',
            field=models.ForeignKey(help_text='Associated visualization request', on_delete=django.db.models.deletion.CASCADE, related_name='results', to='api.visualizationrequest'),
        ),
        migrations.AlterField(
            model_name='screentype',
            name='description',
            field=models.TextField(blank=True, help_text='Detailed description of the screen type'),
        ),
        migrations.AlterField(
            model_name='screentype',
            name='name',
            field=models.CharField(help_text='Screen type name (e.g., Security, Solar, Insect)', max_length=100, unique=True),
        ),
        migrations.AlterField(
            model_name='userprofile',
            name='user',
            field=models.OneToOneField(help_text='Associated user account', on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='visualizationrequest',
            name='original_image',
            field=models.ImageField(help_text='Original image to be processed', upload_to=api.models.upload_to_originals, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'webp']), api.models.validate_image_size, api.models.validate_image_dimensions]),
        ),
        migrations.AlterField(
            model_name='visualizationrequest',
            name='screen_type',
            field=models.ForeignKey(blank=True, help_text='Type of screen to overlay', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='visualization_requests', to='api.screentype'),
        ),
        migrations.AlterField(
            model_name='visualizationrequest',
            name='status',
            field=models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('complete', 'Complete'), ('failed', 'Failed')], default='pending', help_text='Current processing status', max_length=20),
        ),
        migrations.AlterField(
            model_name='visualizationrequest',
            name='task_id',
            field=models.CharField(blank=True, help_text='Background task ID for async processing', max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='visualizationrequest',
            name='user',
            field=models.ForeignKey(help_text='User who made the request', on_delete=django.db.models.deletion.CASCADE, related_name='visualization_requests', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='generatedimage',
            index=models.Index(fields=['request', '-generated_at'], name='api_generat_request_774300_idx'),
        ),
        migrations.AddIndex(
            model_name='generatedimage',
            index=models.Index(fields=['generated_at'], name='api_generat_generat_2e51dc_idx'),
        ),
        migrations.AddIndex(
            model_name='screentype',
            index=models.Index(fields=['name'], name='api_screent_name_2a52fe_idx'),
        ),
        migrations.AddIndex(
            model_name='screentype',
            index=models.Index(fields=['is_active'], name='api_screent_is_acti_2d5b17_idx'),
        ),
        migrations.AddIndex(
            model_name='visualizationrequest',
            index=models.Index(fields=['user', '-created_at'], name='api_visuali_user_id_8775b1_idx'),
        ),
        migrations.AddIndex(
            model_name='visualizationrequest',
            index=models.Index(fields=['status'], name='api_visuali_status_1dafb6_idx'),
        ),
        migrations.AddIndex(
            model_name='visualizationrequest',
            index=models.Index(fields=['created_at'], name='api_visuali_created_f6f0bb_idx'),
        ),
    ]
