name: Publish Package

on:
  workflow_dispatch:
  release:
    types: [published]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        timeout-minutes: 5 # See https://github.com/actions/cache/issues/810
        with:
          cache: 'yarn'
          node-version: '18.x'
          registry-url: 'https://registry.npmjs.org'

      - run: yarn install --network-timeout 1000000 --frozen-lockfile
      - run: yarn test
      - run: npm publish
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN_ELEVATED }}
