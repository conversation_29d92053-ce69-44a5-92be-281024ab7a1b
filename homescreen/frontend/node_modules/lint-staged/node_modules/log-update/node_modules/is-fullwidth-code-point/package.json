{"name": "is-fullwidth-code-point", "version": "5.0.0", "description": "Check if the character represented by a given Unicode code point is fullwidth", "license": "MIT", "repository": "sindresorhus/is-fullwidth-code-point", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["fullwidth", "full-width", "full", "width", "unicode", "character", "string", "codepoint", "code", "point", "is", "detect", "check", "east-asian-width"], "devDependencies": {"ava": "^5.3.1", "tsd": "^0.29.0", "xo": "^0.56.0"}, "dependencies": {"get-east-asian-width": "^1.0.0"}}