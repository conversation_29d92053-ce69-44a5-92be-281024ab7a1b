{"name": "whatwg-encoding", "description": "Decode strings according to the WHATWG Encoding Standard", "keywords": ["encoding", "whatwg"], "version": "2.0.0", "author": "Domenic Denicola <<EMAIL>> (https://domenic.me/)", "license": "MIT", "repository": "jsdom/whatwg-encoding", "main": "lib/whatwg-encoding.js", "files": ["lib/"], "scripts": {"test": "mocha", "lint": "eslint .", "prepare": "node scripts/update.js"}, "dependencies": {"iconv-lite": "0.6.3"}, "devDependencies": {"@domenic/eslint-config": "^1.3.0", "eslint": "^7.32.0", "minipass-fetch": "^1.4.1", "mocha": "^9.1.1"}, "engines": {"node": ">=12"}}