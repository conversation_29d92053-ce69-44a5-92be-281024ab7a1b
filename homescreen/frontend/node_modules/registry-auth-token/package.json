{"name": "registry-auth-token", "version": "3.3.2", "description": "Get the auth token set for an npm registry (if any)", "main": "index.js", "scripts": {"test": "mocha", "posttest": "standard", "coverage": "istanbul cover _mocha"}, "repository": {"type": "git", "url": "git+ssh://**************/rexxars/registry-auth-token.git"}, "keywords": ["npm", "conf", "config", "npmconf", "registry", "auth", "token", "authtoken"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/rexxars/registry-auth-token/issues"}, "homepage": "https://github.com/rexxars/registry-auth-token#readme", "dependencies": {"rc": "^1.1.6", "safe-buffer": "^5.0.1"}, "devDependencies": {"istanbul": "^0.4.2", "mocha": "^3.3.0", "require-uncached": "^1.0.2", "standard": "^10.0.2"}, "standard": {"ignore": ["coverage/**"]}}