{"name": "ansi-align", "version": "3.0.1", "description": "align-text with ANSI support for CLIs", "main": "index.js", "scripts": {"pretest": "standard", "test": "nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls", "release": "standard-version"}, "files": ["index.js"], "repository": {"type": "git", "url": "git+https://github.com/nexdrew/ansi-align.git"}, "keywords": ["ansi", "align", "cli", "center", "pad"], "author": "nexdrew", "license": "ISC", "bugs": {"url": "https://github.com/nexdrew/ansi-align/issues"}, "homepage": "https://github.com/nexdrew/ansi-align#readme", "dependencies": {"string-width": "^4.1.0"}, "devDependencies": {"ava": "^2.0.0", "chalk": "^2.4.2", "coveralls": "^3.0.3", "nyc": "^14.0.0", "standard": "^14.0.0", "standard-version": "^7.0.0"}}