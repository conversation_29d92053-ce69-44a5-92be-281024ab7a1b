{"name": "serve", "version": "14.2.4", "description": "Static file serving and directory listing", "keywords": ["vercel", "serve", "micro", "http-server"], "repository": "vercel/serve", "license": "MIT", "type": "module", "bin": {"serve": "./build/main.js"}, "files": ["build/"], "engines": {"node": ">= 14"}, "dependencies": {"@zeit/schemas": "2.36.0", "ajv": "8.12.0", "arg": "5.0.2", "boxen": "7.0.0", "chalk": "5.0.1", "chalk-template": "0.4.0", "clipboardy": "3.0.0", "compression": "1.7.4", "is-port-reachable": "4.0.0", "serve-handler": "6.1.6", "update-check": "1.5.4"}, "devDependencies": {"@types/compression": "1.7.2", "@types/serve-handler": "6.1.1", "@vercel/style-guide": "3.0.0", "@vitest/coverage-v8": "2.1.3", "eslint": "8.19.0", "got": "12.1.0", "husky": "8.0.1", "lint-staged": "13.0.3", "prettier": "2.7.1", "tsup": "8.3.0", "tsx": "4.19.1", "typescript": "5.6.3", "vitest": "2.1.3"}, "tsup": {"target": "esnext", "format": ["esm"], "outDir": "./build/"}, "prettier": "@vercel/style-guide/prettier", "eslintConfig": {"extends": ["./node_modules/@vercel/style-guide/eslint/node.js", "./node_modules/@vercel/style-guide/eslint/typescript.js"], "parserOptions": {"project": "tsconfig.json"}}, "lint-staged": {"*": ["prettier --ignore-unknown --write"], "source/**/*.ts": ["eslint --max-warnings 0 --fix", "vitest related --run"], "tests": ["vitest --run"]}, "packageManager": "pnpm@9.12.1+sha512.e5a7e52a4183a02d5931057f7a0dbff9d5e9ce3161e33fa68ae392125b79282a8a8a470a51dfc8a0ed86221442eb2fb57019b0990ed24fab519bf0e1bc5ccfc4", "scripts": {"develop": "tsx watch ./source/main.ts", "start": "node ./build/main.js", "compile": "tsup ./source/main.ts", "test:tsc": "tsc --project tsconfig.json", "test:unit": "vitest run --config config/vitest.ts", "test:watch": "vitest watch --config config/vitest.ts", "test": "pnpm test:tsc && pnpm test:unit", "lint:code": "eslint --max-warnings 0 source/**/*.ts", "lint:style": "prettier --check --ignore-path .gitignore .", "lint": "pnpm lint:code && pnpm lint:style", "format": "prettier --write --ignore-path .gitignore ."}}