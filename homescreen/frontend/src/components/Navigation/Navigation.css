/* Navigation Styles */
.navigation {
  background: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.navigation-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 4rem;
}

.navigation-brand {
  flex-shrink: 0;
}

.navigation-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.navigation-desktop {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.navigation-links {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.navigation-link {
  color: #4b5563;
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 0;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease-in-out;
}

.navigation-link:hover {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
}

.navigation-link:focus {
  outline: none;
  color: #3b82f6;
  border-bottom-color: #3b82f6;
}

.navigation-user {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.navigation-username {
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
}

.navigation-mobile-toggle {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
}

.navigation-mobile-toggle:hover {
  background-color: #f3f4f6;
}

.navigation-mobile-toggle:focus {
  outline: none;
  background-color: #f3f4f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.navigation-hamburger {
  display: flex;
  flex-direction: column;
  width: 1.5rem;
  height: 1.125rem;
  justify-content: space-between;
}

.navigation-hamburger span {
  display: block;
  height: 2px;
  width: 100%;
  background-color: #374151;
  border-radius: 1px;
  transition: all 0.3s ease-in-out;
}

.navigation-mobile-menu {
  display: none;
  background: #ffffff;
  border-top: 1px solid #e5e7eb;
  padding: 1rem;
}

.navigation-mobile-menu-open {
  display: block;
}

.navigation-mobile-links {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.navigation-mobile-link {
  color: #4b5563;
  text-decoration: none;
  font-weight: 500;
  padding: 0.75rem 1rem;
  border-radius: 0.375rem;
  transition: background-color 0.2s;
}

.navigation-mobile-link:hover {
  background-color: #f3f4f6;
  color: #3b82f6;
}

.navigation-mobile-user {
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.navigation-mobile-username {
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 1rem;
  text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .navigation-container {
    padding: 0 0.75rem;
  }
  
  .navigation-desktop {
    display: none;
  }
  
  .navigation-mobile-toggle {
    display: block;
  }
  
  .navigation-title {
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .navigation-container {
    padding: 0 0.5rem;
    height: 3.5rem;
  }
  
  .navigation-title {
    font-size: 1.125rem;
  }
  
  .navigation-mobile-menu {
    padding: 0.75rem;
  }
}

/* Animation for mobile menu toggle */
.navigation-mobile-toggle[aria-expanded="true"] .navigation-hamburger span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.navigation-mobile-toggle[aria-expanded="true"] .navigation-hamburger span:nth-child(2) {
  opacity: 0;
}

.navigation-mobile-toggle[aria-expanded="true"] .navigation-hamburger span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Focus styles for accessibility */
.navigation-link:focus,
.navigation-mobile-link:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .navigation {
    border-bottom-width: 2px;
  }
  
  .navigation-link,
  .navigation-mobile-link {
    border: 1px solid transparent;
  }
  
  .navigation-link:hover,
  .navigation-link:focus,
  .navigation-mobile-link:hover,
  .navigation-mobile-link:focus {
    border-color: currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .navigation-link,
  .navigation-mobile-link,
  .navigation-mobile-toggle,
  .navigation-hamburger span {
    transition: none;
  }
}
