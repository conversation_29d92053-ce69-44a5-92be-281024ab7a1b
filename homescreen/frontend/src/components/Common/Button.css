/* <PERSON><PERSON> Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: 1px solid transparent;
  border-radius: 0.375rem;
  font-weight: 500;
  text-align: center;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  white-space: nowrap;
  user-select: none;
}

.btn:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

/* Size variants */
.btn-small {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.btn-medium {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.btn-large {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  line-height: 1.5rem;
}

/* Variant styles */
.btn-primary {
  background-color: #3b82f6;
  border-color: #3b82f6;
  color: #ffffff;
}

.btn-primary:hover:not(.btn-disabled) {
  background-color: #2563eb;
  border-color: #2563eb;
}

.btn-secondary {
  background-color: #6b7280;
  border-color: #6b7280;
  color: #ffffff;
}

.btn-secondary:hover:not(.btn-disabled) {
  background-color: #4b5563;
  border-color: #4b5563;
}

.btn-outline {
  background-color: transparent;
  border-color: #d1d5db;
  color: #374151;
}

.btn-outline:hover:not(.btn-disabled) {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

.btn-ghost {
  background-color: transparent;
  border-color: transparent;
  color: #3b82f6;
}

.btn-ghost:hover:not(.btn-disabled) {
  background-color: #eff6ff;
}

.btn-danger {
  background-color: #ef4444;
  border-color: #ef4444;
  color: #ffffff;
}

.btn-danger:hover:not(.btn-disabled) {
  background-color: #dc2626;
  border-color: #dc2626;
}

/* State modifiers */
.btn-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-full-width {
  width: 100%;
}

.btn-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-content-loading {
  opacity: 0.7;
}
