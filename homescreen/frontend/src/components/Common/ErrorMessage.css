/* Error Message Styles */
.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border-radius: 0.375rem;
  border: 1px solid;
  margin: 0.5rem 0;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.error-message-icon {
  flex-shrink: 0;
  font-size: 1rem;
}

.error-message-text {
  flex: 1;
}

.error-message-dismiss {
  flex-shrink: 0;
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
}

/* Type variants */
.error-message-error {
  background-color: #fef2f2;
  border-color: #fecaca;
  color: #dc2626;
}

.error-message-error .error-message-dismiss:hover {
  background-color: #fecaca;
}

.error-message-warning {
  background-color: #fffbeb;
  border-color: #fed7aa;
  color: #d97706;
}

.error-message-warning .error-message-dismiss:hover {
  background-color: #fed7aa;
}

.error-message-info {
  background-color: #eff6ff;
  border-color: #bfdbfe;
  color: #2563eb;
}

.error-message-info .error-message-dismiss:hover {
  background-color: #bfdbfe;
}

.error-message-success {
  background-color: #f0fdf4;
  border-color: #bbf7d0;
  color: #16a34a;
}

.error-message-success .error-message-dismiss:hover {
  background-color: #bbf7d0;
}
