/* Loading Spinner Styles */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.spinner-circle {
  border-radius: 50%;
  border-style: solid;
  animation: spin 1s linear infinite;
}

/* Size variants */
.spinner-small .spinner-circle {
  width: 1rem;
  height: 1rem;
  border-width: 2px;
}

.spinner-medium .spinner-circle {
  width: 2rem;
  height: 2rem;
  border-width: 3px;
}

.spinner-large .spinner-circle {
  width: 3rem;
  height: 3rem;
  border-width: 4px;
}

/* Color variants */
.spinner-primary .spinner-circle {
  border-color: #007bff transparent #007bff transparent;
}

.spinner-secondary .spinner-circle {
  border-color: #6c757d transparent #6c757d transparent;
}

.spinner-white .spinner-circle {
  border-color: #ffffff transparent #ffffff transparent;
}

.spinner-text {
  font-size: 0.875rem;
  color: #6c757d;
  text-align: center;
}

.spinner-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.spinner-overlay .spinner-text {
  color: #333;
  font-weight: 500;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
