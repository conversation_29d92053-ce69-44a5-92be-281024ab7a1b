/* Image Uploader Styles */
.image-uploader {
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

.upload-area {
  border: 2px dashed #d1d5db;
  border-radius: 0.5rem;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  background-color: #f9fafb;
  position: relative;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-area:hover:not(.upload-area-disabled) {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.upload-area:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.upload-area-has-image {
  padding: 0;
  border: none;
  background-color: transparent;
  cursor: default;
  min-height: auto;
}

.upload-area-drag-over {
  border-color: #10b981;
  background-color: #ecfdf5;
}

.upload-area-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #f3f4f6;
}

.image-uploader-disabled {
  opacity: 0.7;
  pointer-events: none;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  width: 100%;
}

.upload-icon {
  font-size: 3rem;
  opacity: 0.5;
}

.upload-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.upload-primary-text {
  margin: 0;
  color: #374151;
  font-weight: 500;
  font-size: 1rem;
}

.upload-secondary-text {
  margin: 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.upload-constraints {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.upload-constraints p {
  margin: 0.25rem 0;
  color: #6b7280;
  font-size: 0.75rem;
}

.file-input {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.image-preview-container {
  position: relative;
  display: inline-block;
  max-width: 100%;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.image-preview {
  max-width: 100%;
  max-height: 400px;
  width: auto;
  height: auto;
  display: block;
}

.image-preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}

.image-preview-container:hover .image-preview-overlay {
  opacity: 1;
}

.file-info {
  margin-top: 1rem;
  padding: 1rem;
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  font-size: 0.875rem;
}

.file-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0.5rem 0;
}

.file-info-item:first-child {
  margin-top: 0;
}

.file-info-item:last-child {
  margin-bottom: 0;
}

.file-info-label {
  font-weight: 500;
  color: #374151;
}

.file-info-value {
  color: #6b7280;
  word-break: break-all;
  text-align: right;
  margin-left: 1rem;
}

/* Responsive design */
@media (max-width: 640px) {
  .upload-area {
    padding: 1.5rem 1rem;
    min-height: 150px;
  }
  
  .upload-icon {
    font-size: 2rem;
  }
  
  .upload-primary-text {
    font-size: 0.875rem;
  }
  
  .file-info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .file-info-value {
    text-align: left;
    margin-left: 0;
  }
}
