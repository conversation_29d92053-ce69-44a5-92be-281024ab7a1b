/* Grid System Styles */

/* Container */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.container-fluid {
  max-width: none;
  padding: 0 1rem;
}

/* Row */
.row {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}

/* Gutter sizes */
.row-gutter-none {
  margin: 0;
}

.row-gutter-none > .col {
  padding: 0;
}

.row-gutter-small {
  margin: -0.25rem;
}

.row-gutter-small > .col {
  padding: 0.25rem;
}

.row-gutter-medium {
  margin: -0.5rem;
}

.row-gutter-medium > .col {
  padding: 0.5rem;
}

.row-gutter-large {
  margin: -1rem;
}

.row-gutter-large > .col {
  padding: 1rem;
}

/* Row alignment */
.row-align-start {
  align-items: flex-start;
}

.row-align-center {
  align-items: center;
}

.row-align-end {
  align-items: flex-end;
}

.row-align-stretch {
  align-items: stretch;
}

/* Row justification */
.row-justify-start {
  justify-content: flex-start;
}

.row-justify-center {
  justify-content: center;
}

.row-justify-end {
  justify-content: flex-end;
}

.row-justify-between {
  justify-content: space-between;
}

.row-justify-around {
  justify-content: space-around;
}

.row-justify-evenly {
  justify-content: space-evenly;
}

/* Column base */
.col {
  flex: 1;
  min-width: 0;
  padding: 0.5rem;
}

/* Column sizes - Mobile first (xs) */
.col-xs-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-xs-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-xs-3 { flex: 0 0 25%; max-width: 25%; }
.col-xs-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-xs-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
.col-xs-6 { flex: 0 0 50%; max-width: 50%; }
.col-xs-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
.col-xs-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-xs-9 { flex: 0 0 75%; max-width: 75%; }
.col-xs-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
.col-xs-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
.col-xs-12 { flex: 0 0 100%; max-width: 100%; }

/* Small screens (sm) - 576px and up */
@media (min-width: 576px) {
  .container {
    padding: 0 1.5rem;
  }
  
  .col-sm-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
  .col-sm-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
  .col-sm-3 { flex: 0 0 25%; max-width: 25%; }
  .col-sm-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .col-sm-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
  .col-sm-6 { flex: 0 0 50%; max-width: 50%; }
  .col-sm-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
  .col-sm-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
  .col-sm-9 { flex: 0 0 75%; max-width: 75%; }
  .col-sm-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
  .col-sm-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
  .col-sm-12 { flex: 0 0 100%; max-width: 100%; }
}

/* Medium screens (md) - 768px and up */
@media (min-width: 768px) {
  .col-md-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
  .col-md-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
  .col-md-3 { flex: 0 0 25%; max-width: 25%; }
  .col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .col-md-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
  .col-md-6 { flex: 0 0 50%; max-width: 50%; }
  .col-md-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
  .col-md-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
  .col-md-9 { flex: 0 0 75%; max-width: 75%; }
  .col-md-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
  .col-md-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
  .col-md-12 { flex: 0 0 100%; max-width: 100%; }
}

/* Large screens (lg) - 992px and up */
@media (min-width: 992px) {
  .col-lg-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
  .col-lg-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
  .col-lg-3 { flex: 0 0 25%; max-width: 25%; }
  .col-lg-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .col-lg-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
  .col-lg-6 { flex: 0 0 50%; max-width: 50%; }
  .col-lg-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
  .col-lg-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
  .col-lg-9 { flex: 0 0 75%; max-width: 75%; }
  .col-lg-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
  .col-lg-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
  .col-lg-12 { flex: 0 0 100%; max-width: 100%; }
}

/* Extra large screens (xl) - 1200px and up */
@media (min-width: 1200px) {
  .col-xl-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
  .col-xl-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
  .col-xl-3 { flex: 0 0 25%; max-width: 25%; }
  .col-xl-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .col-xl-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
  .col-xl-6 { flex: 0 0 50%; max-width: 50%; }
  .col-xl-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
  .col-xl-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
  .col-xl-9 { flex: 0 0 75%; max-width: 75%; }
  .col-xl-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
  .col-xl-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
  .col-xl-12 { flex: 0 0 100%; max-width: 100%; }
}

/* Offset classes */
.col-offset-1 { margin-left: 8.333333%; }
.col-offset-2 { margin-left: 16.666667%; }
.col-offset-3 { margin-left: 25%; }
.col-offset-4 { margin-left: 33.333333%; }
.col-offset-5 { margin-left: 41.666667%; }
.col-offset-6 { margin-left: 50%; }
.col-offset-7 { margin-left: 58.333333%; }
.col-offset-8 { margin-left: 66.666667%; }
.col-offset-9 { margin-left: 75%; }
.col-offset-10 { margin-left: 83.333333%; }
.col-offset-11 { margin-left: 91.666667%; }

/* Responsive container padding */
@media (max-width: 575px) {
  .container,
  .container-fluid {
    padding: 0 0.75rem;
  }
}

@media (max-width: 480px) {
  .container,
  .container-fluid {
    padding: 0 0.5rem;
  }
}
