import React from 'react';
import './App.css';

function App() {
  return (
    <div className="App">
      <header className="App-header">
        <h1>🏠 Homescreen Visualizer</h1>
        <p>Welcome to the Homescreen Visualizer Application!</p>
        <div style={{ marginTop: '20px' }}>
          <h2>🔐 Login Credentials:</h2>
          <p><strong>Username:</strong> testuser</p>
          <p><strong>Password:</strong> password123</p>
        </div>
        <div style={{ marginTop: '20px' }}>
          <h2>🚀 Features:</h2>
          <ul style={{ textAlign: 'left', maxWidth: '400px' }}>
            <li>User Authentication with JWT</li>
            <li>Image Upload & Processing</li>
            <li>Screen Type Selection</li>
            <li>Visualization Generation</li>
            <li>Results Management</li>
          </ul>
        </div>
        <div style={{ marginTop: '20px' }}>
          <h2>📊 API Status:</h2>
          <p>✅ Backend API: Running at http://localhost:8000/api/</p>
          <p>✅ Authentication: Working</p>
          <p>✅ Screen Types: 3 types available</p>
        </div>
        <div style={{ marginTop: '30px' }}>
          <button
            style={{
              padding: '10px 20px',
              fontSize: '16px',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer'
            }}
            onClick={() => window.location.href = '/api/auth/login/'}
          >
            Test API Login
          </button>
        </div>
      </header>
    </div>
  );
}

export default App;
