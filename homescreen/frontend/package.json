{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.0", "axios": "^1.8.4", "prop-types": "^15.8.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.1", "react-scripts": "5.0.1", "web-vitals": "^2.1.4", "zustand": "^5.0.3"}, "scripts": {"start": "react-scripts start", "start:prod": "serve -s build -l 3000", "build": "react-scripts build", "build:analyze": "npm run build && npx serve -s build", "build:prod": "NODE_ENV=production npm run build", "build:staging": "NODE_ENV=staging npm run build", "test": "react-scripts test", "test:unit": "react-scripts test --watchAll=false", "test:unit:coverage": "react-scripts test --watchAll=false --coverage", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "test:all": "npm run test:unit && npm run test:e2e", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write src/**/*.{js,jsx,ts,tsx,css,md}", "format:check": "prettier --check src/**/*.{js,jsx,ts,tsx,css,md}", "clean": "rm -rf build node_modules/.cache", "precommit": "npm run lint && npm run format:check && npm run test:unit", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "src/**/*.{css,scss,md}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run test:unit"}}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "cypress": "^14.4.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "husky": "^9.1.7", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^16.0.0", "prettier": "^3.5.3", "serve": "^14.2.4"}}