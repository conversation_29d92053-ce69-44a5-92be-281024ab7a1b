repos:
  # General hooks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-case-conflict
      - id: check-merge-conflict
      - id: check-json
      - id: pretty-format-json
        args: ['--autofix']
      - id: check-toml
      - id: check-xml
      - id: debug-statements
      - id: detect-private-key

  # Python formatting and linting
  - repo: https://github.com/psf/black
    rev: 23.11.0
    hooks:
      - id: black
        language_version: python3.11

  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: ["--profile", "black"]

  - repo: https://github.com/pycqa/flake8
    rev: 6.1.0
    hooks:
      - id: flake8
        additional_dependencies: [flake8-docstrings, flake8-import-order]

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.7.1
    hooks:
      - id: mypy
        additional_dependencies: [django-stubs, djangorestframework-stubs]

  # Security
  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: ["-c", "pyproject.toml"]
        additional_dependencies: ["bandit[toml]"]

  # Django specific
  - repo: local
    hooks:
      - id: django-check
        name: Django Check
        entry: python manage.py check
        language: system
        pass_filenames: false
        files: \.py$

      - id: django-check-migrations
        name: Django Check Migrations
        entry: python manage.py makemigrations --check --dry-run
        language: system
        pass_filenames: false
        files: \.py$

  # Frontend hooks
  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v8.54.0
    hooks:
      - id: eslint
        files: frontend/src/.*\.[jt]sx?$
        types: [file]
        additional_dependencies:
          - eslint@8.54.0
          - eslint-plugin-react@7.33.2
          - eslint-plugin-react-hooks@4.6.0

  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.1.0
    hooks:
      - id: prettier
        files: frontend/src/.*\.(js|jsx|ts|tsx|css|md)$
        additional_dependencies:
          - prettier@3.1.0

  # Commit message formatting
  - repo: https://github.com/commitizen-tools/commitizen
    rev: v3.13.0
    hooks:
      - id: commitizen
        stages: [commit-msg]

  # Documentation
  - repo: https://github.com/pycqa/doc8
    rev: v1.1.1
    hooks:
      - id: doc8
        args: ['--max-line-length=88']

# Configuration
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit.com hooks

    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: []
  submodules: false
