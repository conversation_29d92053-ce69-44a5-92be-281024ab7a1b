version: '3.8'

services:
  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: homescreen
      POSTGRES_USER: homescreen
      POSTGRES_PASSWORD: homescreen123
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U homescreen"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  web:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DEBUG=False
      - DATABASE_URL=*********************************************/homescreen
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-here
      - ALLOWED_HOSTS=localhost,127.0.0.1
      - DJANGO_SUPERUSER_USERNAME=admin
      - DJANGO_SUPERUSER_EMAIL=<EMAIL>
      - DJANGO_SUPERUSER_PASSWORD=admin123
    volumes:
      - media_data:/app/media
      - static_data:/app/static
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  postgres_data:
  redis_data:
  media_data:
  static_data:
