Django>=4.0 # Or a specific compatible version
djangorestframework>=3.14 # Or a specific compatible version
django-cors-headers>=4.3.0 # For CORS support
django-filter>=23.0 # For API filtering
djangorestframework-simplejwt>=5.5.0 # For JWT authentication
django-ratelimit>=4.1.0 # For rate limiting
Pillow>=10.0.0 # For image processing
psycopg2-binary # PostgreSQL adapter
gunicorn>=21.0.0 # WSGI server for production
gevent>=23.0.0 # Async worker for gunicorn
redis>=5.0.0 # Redis client
celery>=5.3.0 # Task queue
django-storages>=1.14.0 # Cloud storage support
boto3>=1.34.0 # AWS SDK for S3 storage
whitenoise>=6.6.0 # Static file serving
django-health-check>=3.17.0 # Health check endpoints
